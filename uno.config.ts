import { defineConfig } from 'unocss';
import presetUno from '@unocss/preset-uno';
import presetAttributify from '@unocss/preset-attributify';
import presetIcons from '@unocss/preset-icons';
import transformerDirectives from '@unocss/transformer-directives';
import transformerVariantGroup from '@unocss/transformer-variant-group';

export default defineConfig({
  presets: [
    presetUno(), // 基础工具类预设
    presetAttributify(), // 属性模式预设
    presetIcons({
      // 可选：配置图标预设
      warn: true,
    }),
  ],
  transformers: [
    transformerDirectives(), // 支持 @apply 等指令
    transformerVariantGroup(), // 支持变量组语法
  ],
  // 自定义主题颜色
  theme: {
    colors: {
      // 页面背景色
      'page-bg': '#0D0D0D',
      // 按钮背景色
      primary: '#FF5E15',
      // form表单label颜色
      // 文本颜色建议命名为 text-label，更符合语义
      label: '#656565',
      // form表单项颜色
      'form-item': '#C9C9C9',
    },
  },
  // 确保扫描所有相关文件
  content: {
    filesystem: ['src/**/*.{vue,js,ts,jsx,tsx}', 'index.html'],
  },
});
