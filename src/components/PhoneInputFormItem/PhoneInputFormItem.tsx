import React, { useEffect } from 'react';
import PhoneInput from './PhoneInput';
import { Form } from 'antd';
import {
  parsePhoneNumberFromString,
  type CountryCode,
} from 'libphonenumber-js/mobile';
import { parsePhoneNumber } from '@/utils/utils';
import { useLanguage } from '@/hooks/useLanguage';

// 验证手机号
function validatePhone(phoneNumber: string) {
  const { phone, phoneCountry } = parsePhoneNumber(phoneNumber);
  const phoneNumberObj = parsePhoneNumberFromString(
    `${phone}`,
    phoneCountry.toUpperCase() as CountryCode
  );

  return phoneNumberObj ? phoneNumberObj.isValid() : false;
}

const PhoneInputFormItem: React.FC = (initialPhone: string) => {
  const { t, isZhCN } = useLanguage();
  const form = Form.useFormInstance();
  // 初始化手机号
  useEffect(() => {
    if (initialPhone) {
      form.setFieldsValue({ phone: initialPhone });
    }
  }, [initialPhone]);

  return (
    <Form.Item
      label={t('auth.register.step3.form.phone')}
      name="phone"
      rules={[
        {
          validator: (_, value) => {
            console.log('value----', value);
            if (!value) {
              return Promise.resolve(); // 手机号非必填
            }
            if (!validatePhone(`${value}`)) {
              return Promise.reject(new Error(t('common.form.phoneInvalid')));
            }
            return Promise.resolve();
          },
        },
      ]}
    >
      <PhoneInput
        placeholder={t('common.form.phonePlaceholder')}
        size="large"
        defaultCountry={isZhCN ? 'CN' : 'US'}
        inputClassName="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
      />
    </Form.Item>
  );
};

export default PhoneInputFormItem;
