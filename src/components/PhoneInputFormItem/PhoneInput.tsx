import React, { useState, useEffect, useMemo } from 'react';
import { ConfigProvider, Input, Select } from 'antd';
import {
  parsePhoneNumberFromString,
  getCountries,
  getCountryCallingCode,
  type CountryCode,
} from 'libphonenumber-js/mobile';
import { useLanguage } from '@/hooks/useLanguage';
import { parsePhoneNumber } from '@/utils/utils';

interface PhoneInputProps {
  value?: string;
  onChange?: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  size?: 'small' | 'middle' | 'large';
  defaultCountry?: CountryCode;
  className?: string;
  inputClassName?: string;
  error?: string;
  style?: React.CSSProperties;
}

/**
 * 国际电话号码输入组件
 * 基于 libphonenumber-js 和 Ant Design，支持搜索的国家代码选择
 */
const PhoneInput: React.FC<PhoneInputProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  size = 'large',
  defaultCountry = 'US',
  className = '',
  inputClassName = '',
  error,
  style,
}) => {
  const { t, language } = useLanguage();
  const [selectedCountry, setSelectedCountry] =
    useState<CountryCode>(defaultCountry);
  const [phoneNumber, setPhoneNumber] = useState<string>('');

  // 获取所有支持的国家
  const countries = useMemo(() => {
    const allCountries = getCountries();

    // 优先显示
    const priorityCountries: CountryCode[] = [
      'US',
      'CN',
      'CA',
      'GB',
      'AU',
      'JP',
      'KR',
      'SG',
      'HK',
      'TW',
    ];
    const sortedCountries = [
      ...priorityCountries.filter(country => allCountries.includes(country)),
      ...allCountries.filter(country => !priorityCountries.includes(country)),
    ];

    return sortedCountries
      .map(country => {
        const callingCode = getCountryCallingCode(country); // 国家数字码

        return {
          value: country,
          label: `${country} (+${callingCode})`,
          callingCode,
        };
      })
      .filter(Boolean) as Array<{
      value: string;
      label: string;
      callingCode: string;
    }>;
  }, [language]);

  // 解析初始值
  useEffect(() => {
    if (value) {
      try {
        const { phone, phoneCountry } = parsePhoneNumber(value);
        if (phone) {
          setSelectedCountry(phoneCountry as CountryCode);
          setPhoneNumber(phone.toString());
        }
      } catch (error) {
        console.warn('解析手机号失败', error);
      }
    }
  }, []);

  // 处理国家选择变化
  const handleCountryChange = (country: CountryCode) => {
    setSelectedCountry(country);

    if (phoneNumber) {
      try {
        // 完整的手机号，传给父组件 格式为：15678901234~CN
        onChange?.(`${phoneNumber}~${country}`);
      } catch (error) {
        console.warn('更新国家后手机号失败:', error);
      }
    }
  };

  // 处理电话号码输入变化
  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;

    // 只允许数字、空格、括号、短横线
    const cleanValue = inputValue.replace(/[^\d\s\-\(\)]/g, '');
    const digitsOnly = cleanValue.replace(/\D/g, '');

    setPhoneNumber(digitsOnly);

    if (digitsOnly) {
      try {
        // 完整的手机号，传给父组件 格式为：15678901234~US
        onChange?.(`${digitsOnly}~${selectedCountry}`);
      } catch (error) {
        console.warn('更新手机号失败:', error);
      }
    } else {
      onChange?.(undefined);
    }
  };

  // 自定义过滤函数
  const filterOption = (input: string, option: any) => {
    const label = option.label || '';

    return label.toLowerCase().includes(input.toLowerCase());
  };

  const labelRender = props => {
    const callingCode = getCountryCallingCode(props.value); // 国家数字码

    return <div>{`+${callingCode}`}</div>;
  };

  return (
    <div
      className={`phone-input-container ${className} flex gap-15px`}
      style={style}
    >
      <ConfigProvider
        theme={{
          components: {
            Select: {
              activeBorderColor: '#c9c9c9',
              selectorBg: '#c9c9c9',
              colorBorder: '#c9c9c9',
              optionSelectedBg: '#c9c9c9',
              borderRadius: 6,
              optionLineHeight: '54px',
              optionHeight: 54,
              colorText: '#000000',
              colorTextPlaceholder: 'rgba(0, 0, 0, 0.25)',
            },
          },
        }}
      >
        <Select
          showSearch
          value={selectedCountry}
          onChange={handleCountryChange}
          size={size || 'large'}
          disabled={disabled}
          placeholder={t('common.form.selectCountry')}
          optionFilterProp="children"
          filterOption={filterOption}
          options={countries}
          labelRender={labelRender}
          popupMatchSelectWidth={110}
          className="!h-54px text-14px flex-shrink-0 flex-basis-88px"
        />
        <Input
          value={phoneNumber}
          onChange={handlePhoneNumberChange}
          placeholder={placeholder || t('common.form.phonePlaceholder')}
          className={
            inputClassName ||
            'placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)'
          }
          size={size}
          disabled={disabled}
          status={error ? 'error' : undefined}
        />
      </ConfigProvider>
    </div>
  );
};

export default PhoneInput;
