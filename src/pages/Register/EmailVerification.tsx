import React from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import FormButton from './coms/FormButton';
import { Link } from 'react-router-dom';
import type { Rule } from 'antd/es/form';

const EmailVerification: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterStore();
  const [form] = Form.useForm();

  // 表单校验成功回调，更新表单数据并跳转到下一步
  const onFinish = (values: { email: string }) => {
    updateFormData(values);
    setCurrentStep(2);
    navigate('/register/alias');
  };

  // 初始化表单值
  React.useEffect(() => {
    if (formData.email) {
      form.setFieldsValue({ email: formData.email });
    }
  }, [formData.email, form]);

  const rules: Rule[] = [
    {
      required: true,
    },
    {
      type: 'email',
      message: t('common.form.emailInvalid'),
    },
  ];
  return (
    <Form form={form} layout="vertical" onFinish={onFinish} autoComplete="off">
      <Form.Item
        name="email"
        label={
          <span className="leading-10">
            {t('auth.register.step1.form.email')}
          </span>
        }
        rules={rules}
      >
        <Input
          placeholder={t('common.form.emailRequired')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>

      <Form.Item>
        <div className="mb-64px">
          <span className="font-inter text-[12px] text-[#656565] font-medium">
            {t('auth.register.step1.hasAccount')}
          </span>

          <Link
            to="/login"
            className="font-inter ml-1 text-[12px] font-medium !text-[#ff5e13] hover:underline"
          >
            {t('auth.register.step1.loginHere')}
          </Link>
        </div>

        <FormButton text={t('common.buttons.continue')} />
      </Form.Item>
    </Form>
  );
};

export default EmailVerification;
