import React from 'react';
import { Card, Form, Input, Button, Typography, message, Row, Col } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import FormButton from './coms/FormButton';
import CountryRegionSelector from '@/components/CountryRegionSelector';
import PhoneInputFormItem from '@/components/PhoneInputFormItem/PhoneInputFormItem';

const PersonalInfo: React.FC = () => {
  const navigate = useNavigate();
  const { t, isEnUS } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterStore();
  const [form] = Form.useForm();

  const onFinish = (values: any) => {
    updateFormData(values);
    setCurrentStep(4);
    message.success('个人信息已保存');
    navigate('/register/password');
  };

  // 初始化表单值
  React.useEffect(() => {
    const initialValues = {
      firstName: formData.firstName,
      lastName: formData.lastName,
      phone: formData.phone,
      address: formData.address,
      countryRegion:
        formData.countryRegion ||
        (formData.country && formData.state
          ? [formData.country, formData.state]
          : undefined),
      postalCode: formData.postalCode,
    };
    form.setFieldsValue(initialValues);
  }, [formData, form]);

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    }
  }, [formData.email, formData.alias, navigate]);

  return (
    <Form form={form} layout="vertical" onFinish={onFinish} autoComplete="off">
      <Form.Item
        label={t('auth.register.step3.form.firstName')}
        name="firstName"
        rules={[
          {
            required: true,
            message: t('auth.register.step3.form.firstNameRequired'),
          },
        ]}
      >
        <Input
          placeholder={t('auth.register.step3.form.firstNamePlaceholder')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>
      <Form.Item
        label={t('auth.register.step3.form.lastName')}
        name="lastName"
        rules={[
          {
            required: true,
            message: t('auth.register.step3.form.lastNameRequired'),
          },
        ]}
      >
        <Input
          placeholder={t('auth.register.step3.form.lastNamePlaceholder')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>

      <PhoneInputFormItem />

      <Form.Item label={t('auth.register.step3.form.address')} name="address">
        <Input
          placeholder={t('auth.register.step3.form.addressPlaceholder')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>

      <Form.Item
        label={t('auth.register.step3.form.country')}
        name="countryRegion"
      >
        <CountryRegionSelector
          placeholder={t('common.form.countryRegionPlaceholder')}
          size="large"
        />
      </Form.Item>

      <Form.Item
        label={t('auth.register.step3.form.postalCode')}
        name="postalCode"
      >
        <Input
          placeholder={t('auth.register.step3.form.postalCodePlaceholder')}
          size="large"
        />
      </Form.Item>

      <FormButton text={t('auth.register.step3.buttons.next')} />
    </Form>
  );
};

export default PersonalInfo;
