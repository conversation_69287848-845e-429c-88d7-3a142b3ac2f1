import React from 'react';
import { Card, Form, Input, Button, Typography, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import FormButton from './coms/FormButton';
import type { Rule } from 'antd/es/form';

const AliasSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, updateFormData, setCurrentStep } = useRegisterStore();
  const [form] = Form.useForm();

  // 初始化表单值
  React.useEffect(() => {
    if (formData.alias) {
      form.setFieldsValue({ alias: formData.alias });
    }
  }, [formData.alias, form]);

  // 检查是否有邮箱数据，如果没有则重定向到第一步
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    }
  }, [formData.email, navigate]);

  // 表单校验成功回调，更新表单数据并跳转到下一步
  const onFinish = () => {
    updateFormData({ alias: form.getFieldValue('alias') });
    setCurrentStep(3);
    navigate('/register/personal-info');
  };

  const rules: Rule[] = [
    {
      required: true,
      message: t('auth.register.step2.form.aliasRequired'),
    },
    {
      min: 6,
      message: t('auth.register.step2.form.aliasMinLength', {
        min: 6,
      }),
    },
    {
      pattern: /^[a-zA-Z0-9]+$/,
      message: t('auth.register.step2.form.aliasPattern'),
    },
    {
      max: 12,
      message: t('auth.register.step2.form.aliasMaxLength', {
        max: 12,
      }),
    },
  ];
  return (
    <Form form={form} layout="vertical" onFinish={onFinish} autoComplete="off">
      <Form.Item
        label={t('auth.register.step2.form.alias')}
        name="alias"
        rules={rules}
      >
        <Input
          placeholder={t('auth.register.step2.form.aliasPlaceholder')}
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
          size="large"
        />
      </Form.Item>
      <Form.Item>
        <div className="mb-64px">
          <span className="font-inter text-[12px] text-[#656565] font-medium">
            {t('auth.register.step2.form.aliasMinLength', {
              min: 6,
            })}
          </span>
        </div>

        <FormButton text={t('auth.register.step2.buttons.next')} />
      </Form.Item>
    </Form>
  );
};

export default AliasSetup;
