import React, { Suspense, useEffect } from 'react';
import { ConfigProvider, Spin } from 'antd';
import '@ant-design/v5-patch-for-react-19';
import 'antd/dist/reset.css';
import zhCN from 'antd/locale/zh_CN';
import enUS from 'antd/locale/en_US';
import AppRouter from './router';
import { useAppStore } from './store';
import './locales'; // 初始化 i18n
import './App.css';
import { useLanguage } from '@/hooks/useLanguage';
import GlobalLoading from '@/components/GlobalLoading';

/**
 * 主应用组件
 * - 配置 Ant Design 中文语言包
 * - 集成路由系统和国际化
 * - 全局状态管理已在各组件中使用
 */
const App: React.FC = () => {
  const { language } = useAppStore();
  const { t } = useLanguage();

  // 根据当前语言选择 Ant Design 语言包
  const antdLocale = language === 'zh' ? zhCN : enUS;
  // 配置 Ant Design form 的验证消息
  const validateMessages = {
    required: t('common.form.requiredMsg', { label: '${label}' }),
  };
  return (
    <ConfigProvider
      locale={antdLocale}
      form={{ validateMessages }}
      theme={{
        components: {
          Form: {
            labelColor: 'var(--label)',
          },
        },
      }}
    >
      <div className="App">
        <Suspense
          fallback={
            <div className="min-h-screen flex items-center justify-center">
              <Spin size="large" />
            </div>
          }
        >
          <AppRouter />
        </Suspense>
        <GlobalLoading />
      </div>
    </ConfigProvider>
  );
};

export default App;
